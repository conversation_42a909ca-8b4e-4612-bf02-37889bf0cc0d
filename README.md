# 公文自动生成工具

## 功能简介

这是一个基于Python开发的自动化公文生成工具，能够根据标准公文格式自动生成Word文档。用户只需输入文档内容，程序会自动套用指定的格式模板，大大提高公文写作效率。

## 主要特性

- ✅ **标准格式**: 符合公文写作规范，包含文头、标题、正文、落款等完整要素
- ✅ **易于使用**: 支持配置文件和交互式两种输入方式
- ✅ **格式美观**: 自动应用标准字体、字号、对齐方式等格式
- ✅ **灵活配置**: 支持自定义机关名称、文号、签发人等信息
- ✅ **批量生成**: 可通过修改配置文件快速生成多个文档

## 环境要求

- Windows 10/11
- Python 3.7+
- Anaconda 或 Miniconda

## 安装步骤

### 1. 自动安装（推荐）

在PowerShell中运行以下命令：

```powershell
# 进入项目目录
cd "你的项目路径"

# 运行安装脚本
.\setup_env.ps1
```

### 2. 手动安装

```powershell
# 创建虚拟环境
conda create -n document_generator python=3.9 -y

# 激活环境
conda activate document_generator

# 安装依赖包
pip install -r requirements.txt
```

## 使用方法

### 方法一：配置文件模式（推荐）

1. **编辑配置文件**
   
   打开 `document_config.yaml` 文件，根据需要修改内容：
   
   ```yaml
   # 发文机关名称（必填）
   org_name: "港信期货有限公司"
   
   # 文档标题（必填）
   title: "关于加强风险管理工作的通知"
   
   # 正文内容（必填）
   content: |
     为进一步加强公司风险管理工作，现就有关事项通知如下：
     一、高度重视风险管理工作...
   ```

2. **生成文档**
   
   ```powershell
   # 激活环境
   conda activate document_generator
   
   # 使用默认配置生成文档
   python document_generator.py
   
   # 指定配置文件和输出文件名
   python document_generator.py -c my_config.yaml -o my_document.docx
   ```

### 方法二：交互式输入模式

```powershell
# 激活环境
conda activate document_generator

# 启动交互式模式
python document_generator.py -i
```

然后按提示输入各项信息即可。

### 方法三：代码调用模式

参考 `example_usage.py` 文件，直接在Python代码中调用：

```python
from document_generator import DocumentGenerator

# 创建生成器
generator = DocumentGenerator()

# 添加文档内容
generator.add_header("机关名称", "文号")
generator.add_title("文档标题")
generator.add_content("正文内容")
generator.add_footer("机关名称")

# 保存文档
generator.save_document("输出文件名.docx")
```

## 配置文件说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| org_name | 字符串 | 是 | 发文机关名称 |
| title | 字符串 | 是 | 文档标题 |
| content | 字符串 | 是 | 正文内容，支持多段落 |
| doc_number | 字符串 | 否 | 发文字号 |
| signer | 字符串 | 否 | 签发人 |
| recipient | 字符串 | 否 | 主送机关 |
| attachments | 列表/字符串 | 否 | 附件说明 |
| date | 字符串 | 否 | 成文日期，格式：2025年1月1日 |

## 测试验证

运行测试脚本验证工具是否正常工作：

```powershell
# 激活环境
conda activate document_generator

# 运行测试
python test_generator.py
```

运行示例脚本查看效果：

```powershell
# 运行示例
python example_usage.py
```

## 常见问题与解决方法

### 1. 安装依赖时出错

**问题**: `pip install` 时提示网络错误或包不存在

**解决方法**:
```powershell
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 字体显示异常

**问题**: 生成的文档中字体不是预期的仿宋或方正小标宋

**原因**: 系统中未安装相应字体

**解决方法**:
- 下载并安装仿宋字体和方正小标宋字体
- 或修改 `document_generator.py` 中的字体设置为系统已有字体

### 3. 文档保存失败

**问题**: 提示"文档保存失败"

**可能原因**:
- 输出文件正在被其他程序打开（如Word）
- 没有写入权限
- 磁盘空间不足

**解决方法**:
- 关闭可能打开该文件的程序
- 检查文件夹权限
- 更换输出路径

### 4. 配置文件格式错误

**问题**: 提示"配置文件格式错误"

**解决方法**:
- 检查YAML文件格式，注意缩进和冒号
- 确保中文内容使用UTF-8编码
- 参考提供的模板文件格式

### 5. 程序运行时中文乱码

**问题**: 控制台显示中文乱码

**解决方法**:
```powershell
# 设置PowerShell编码
chcp 65001
```

## 文件结构

```
项目目录/
├── document_generator.py      # 主程序文件
├── document_config.yaml       # 配置文件模板
├── requirements.txt           # 依赖包列表
├── setup_env.ps1             # 环境安装脚本
├── test_generator.py          # 测试脚本
├── example_usage.py           # 使用示例
└── README.md                  # 说明文档
```

## 技术实现原理

### 1. 文档结构设计
- 使用python-docx库操作Word文档
- 按照公文标准格式定义样式和布局
- 支持段落格式、字体设置、对齐方式等

### 2. 模板化处理
- 将公文各部分抽象为独立的方法
- 支持可选字段的灵活组合
- 自动处理格式和间距

### 3. 配置管理
- 使用YAML格式存储配置信息
- 支持中文内容和特殊字符
- 提供默认值和错误处理

### 4. 用户界面
- 命令行参数解析
- 交互式输入验证
- 友好的错误提示

## 扩展建议

1. **添加更多公文类型**: 可扩展支持请示、报告、函等不同类型
2. **模板管理**: 支持多套模板切换
3. **批量处理**: 支持从Excel等数据源批量生成
4. **格式检查**: 添加公文格式规范性检查功能

## 联系支持

如遇到问题或需要功能扩展，请检查：
1. 是否按照安装步骤正确配置环境
2. 配置文件格式是否正确
3. 依赖包是否完整安装

---

**版本**: 1.0.0  
**更新日期**: 2025年1月  
**适用环境**: Windows + Python 3.7+
