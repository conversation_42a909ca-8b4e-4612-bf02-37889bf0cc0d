#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公文自动生成工具
功能：根据标准公文格式自动生成Word文档
作者：自动化工具
日期：2025年
"""

import os
import sys
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import yaml
import click


class DocumentGenerator:
    """公文生成器类 - 负责创建和格式化Word文档"""
    
    def __init__(self):
        """初始化文档生成器"""
        self.doc = Document()  # 创建新的Word文档对象
        self.setup_styles()    # 设置文档样式
    
    def setup_styles(self):
        """设置文档样式 - 定义标题、正文等格式"""
        try:
            # 设置正文样式
            styles = self.doc.styles
            normal_style = styles['Normal']
            normal_font = normal_style.font
            normal_font.name = '仿宋'      # 公文常用字体
            normal_font.size = Pt(16)      # 三号字体
            
            # 创建标题样式
            if 'Title_Custom' not in [s.name for s in styles]:
                title_style = styles.add_style('Title_Custom', WD_STYLE_TYPE.PARAGRAPH)
                title_font = title_style.font
                title_font.name = '方正小标宋简体'  # 标题字体
                title_font.size = Pt(22)           # 二号字体
                title_font.bold = True
                title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
        except Exception as e:
            print(f"样式设置警告: {e}")
    
    def add_header(self, org_name, doc_number, signer=None):
        """添加文头信息
        参数:
            org_name: 发文机关名称
            doc_number: 发文字号
            signer: 签发人（可选）
        """
        # 发文机关标识
        header_p = self.doc.add_paragraph()
        header_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = header_p.add_run(org_name)
        run.font.name = '方正小标宋简体'
        run.font.size = Pt(22)
        run.font.bold = True
        
        # 分隔线
        line_p = self.doc.add_paragraph()
        line_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
        line_run = line_p.add_run('─' * 30)
        line_run.font.size = Pt(14)
        
        # 发文字号和签发人
        if doc_number or signer:
            info_p = self.doc.add_paragraph()
            if doc_number:
                info_p.add_run(f"发文字号：{doc_number}").font.size = Pt(14)
            if signer:
                if doc_number:
                    info_p.add_run("    ")  # 间距
                info_p.add_run(f"签发人：{signer}").font.size = Pt(14)
            info_p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def add_title(self, title):
        """添加文档标题
        参数:
            title: 文档标题
        """
        title_p = self.doc.add_paragraph()
        title_p.style = 'Title_Custom'
        title_p.add_run(title)
        
        # 标题后添加空行
        self.doc.add_paragraph()
    
    def add_recipient(self, recipient):
        """添加主送机关
        参数:
            recipient: 主送机关
        """
        if recipient:
            recipient_p = self.doc.add_paragraph()
            recipient_p.add_run(f"{recipient}：")
            recipient_p.paragraph_format.first_line_indent = Inches(0)
    
    def add_content(self, content):
        """添加正文内容
        参数:
            content: 正文内容（支持多段落）
        """
        # 如果内容是字符串，按换行符分割成段落
        if isinstance(content, str):
            paragraphs = content.split('\n')
        else:
            paragraphs = content
        
        for para_text in paragraphs:
            if para_text.strip():  # 跳过空行
                para = self.doc.add_paragraph()
                para.add_run(para_text.strip())
                # 设置段落首行缩进（两个字符）
                para.paragraph_format.first_line_indent = Inches(0.5)
    
    def add_attachments(self, attachments):
        """添加附件说明
        参数:
            attachments: 附件列表
        """
        if attachments:
            self.doc.add_paragraph()  # 空行
            attach_p = self.doc.add_paragraph()
            attach_p.add_run("附件：")
            
            if isinstance(attachments, str):
                attach_p.add_run(attachments)
            else:
                for i, attachment in enumerate(attachments, 1):
                    if i > 1:
                        self.doc.add_paragraph().add_run(f"      {i}. {attachment}")
                    else:
                        attach_p.add_run(f"{i}. {attachment}")
    
    def add_footer(self, org_name, date=None):
        """添加文档落款
        参数:
            org_name: 发文机关署名
            date: 成文日期（默认为当前日期）
        """
        if date is None:
            date = datetime.now().strftime("%Y年%m月%d日")
        
        # 添加空行
        self.doc.add_paragraph()
        self.doc.add_paragraph()
        
        # 机关署名（右对齐）
        org_p = self.doc.add_paragraph()
        org_p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        org_p.add_run(org_name)
        
        # 成文日期（右对齐）
        date_p = self.doc.add_paragraph()
        date_p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        date_p.add_run(date)
    
    def save_document(self, filename):
        """保存文档
        参数:
            filename: 保存的文件名
        """
        try:
            self.doc.save(filename)
            return True
        except Exception as e:
            print(f"保存文档时出错: {e}")
            return False


def load_config(config_file):
    """从配置文件加载文档信息
    参数:
        config_file: 配置文件路径
    返回:
        配置信息字典
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"配置文件 {config_file} 不存在")
        return None
    except yaml.YAMLError as e:
        print(f"配置文件格式错误: {e}")
        return None


@click.command()
@click.option('--config', '-c', default='document_config.yaml', 
              help='配置文件路径（默认: document_config.yaml）')
@click.option('--output', '-o', default='generated_document.docx', 
              help='输出文件名（默认: generated_document.docx）')
@click.option('--interactive', '-i', is_flag=True, 
              help='交互式输入模式')
def main(config, output, interactive):
    """公文自动生成工具主程序"""
    
    print("=== 公文自动生成工具 ===")
    print("功能：根据标准格式自动生成Word公文")
    print()
    
    if interactive:
        # 交互式输入模式
        print("请输入公文信息：")
        doc_info = {
            'org_name': input("发文机关名称: "),
            'doc_number': input("发文字号（可选，直接回车跳过）: ") or None,
            'signer': input("签发人（可选，直接回车跳过）: ") or None,
            'title': input("文档标题: "),
            'recipient': input("主送机关（可选，直接回车跳过）: ") or None,
            'content': input("正文内容（用\\n表示换行）: ").replace('\\n', '\n'),
            'attachments': input("附件说明（可选，直接回车跳过）: ") or None,
            'date': input("成文日期（可选，格式：2025年1月1日，直接回车使用当前日期）: ") or None
        }
    else:
        # 从配置文件读取
        doc_info = load_config(config)
        if doc_info is None:
            print("无法读取配置文件，程序退出")
            sys.exit(1)
    
    # 创建文档生成器
    generator = DocumentGenerator()
    
    # 生成文档各部分
    try:
        # 添加文头
        generator.add_header(
            doc_info['org_name'], 
            doc_info.get('doc_number'), 
            doc_info.get('signer')
        )
        
        # 添加标题
        generator.add_title(doc_info['title'])
        
        # 添加主送机关
        generator.add_recipient(doc_info.get('recipient'))
        
        # 添加正文
        generator.add_content(doc_info['content'])
        
        # 添加附件
        if doc_info.get('attachments'):
            generator.add_attachments(doc_info['attachments'])
        
        # 添加落款
        generator.add_footer(doc_info['org_name'], doc_info.get('date'))
        
        # 保存文档
        if generator.save_document(output):
            print(f"✓ 文档生成成功: {output}")
        else:
            print("✗ 文档保存失败")
            sys.exit(1)
            
    except KeyError as e:
        print(f"配置信息缺少必要字段: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"生成文档时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
