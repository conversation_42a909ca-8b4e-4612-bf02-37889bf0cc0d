#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公文生成工具使用示例
演示如何通过代码直接调用生成器创建文档
"""

from document_generator import DocumentGenerator
from datetime import datetime


def create_simple_notice():
    """创建简单通知文档的示例"""
    print("示例1: 创建简单通知文档")
    
    # 创建文档生成器
    generator = DocumentGenerator()
    
    # 文档信息
    org_name = "港信期货有限公司"
    doc_number = "港信期货〔2025〕002号"
    title = "关于元旦放假安排的通知"
    recipient = "全体员工"
    content = """根据国家法定节假日安排，现将2025年元旦放假事宜通知如下：

一、放假时间
2025年1月1日（星期三）放假1天。

二、注意事项
1. 各部门要做好放假前的安全检查工作；
2. 值班人员要坚守岗位，确保各项工作正常运行；
3. 如遇紧急情况，请及时联系相关负责人。

祝全体员工节日快乐！"""
    
    # 生成文档
    generator.add_header(org_name, doc_number)
    generator.add_title(title)
    generator.add_recipient(recipient)
    generator.add_content(content)
    generator.add_footer(org_name)
    
    # 保存文档
    filename = "元旦放假通知.docx"
    if generator.save_document(filename):
        print(f"✓ 文档已生成: {filename}")
    else:
        print("✗ 文档生成失败")


def create_meeting_notice():
    """创建会议通知文档的示例"""
    print("示例2: 创建会议通知文档")
    
    generator = DocumentGenerator()
    
    # 文档信息
    org_name = "港信期货有限公司"
    doc_number = "港信期货〔2025〕003号"
    signer = "办公室"
    title = "关于召开2025年第一季度工作会议的通知"
    recipient = "各部门负责人"
    content = """为总结上年度工作，部署新年度任务，定于近期召开2025年第一季度工作会议。现将有关事项通知如下：

一、会议时间
2025年1月20日（星期一）上午9:00

二、会议地点
公司三楼会议室

三、参会人员
各部门负责人及相关业务骨干

四、会议议程
1. 总结2024年工作情况
2. 分析当前市场形势
3. 部署2025年第一季度重点工作
4. 讨论相关制度完善事宜

五、会议要求
1. 请各部门负责人准时参会，不得无故缺席
2. 请提前准备本部门工作汇报材料
3. 会议期间请将手机调至静音状态

如有特殊情况不能参会，请提前向办公室请假。"""
    
    attachments = ["会议议程", "工作汇报模板"]
    
    # 生成文档
    generator.add_header(org_name, doc_number, signer)
    generator.add_title(title)
    generator.add_recipient(recipient)
    generator.add_content(content)
    generator.add_attachments(attachments)
    generator.add_footer(org_name, "2025年1月15日")
    
    # 保存文档
    filename = "季度工作会议通知.docx"
    if generator.save_document(filename):
        print(f"✓ 文档已生成: {filename}")
    else:
        print("✗ 文档生成失败")


def create_custom_document():
    """创建自定义文档的示例"""
    print("示例3: 创建自定义文档")
    
    generator = DocumentGenerator()
    
    # 用户可以根据需要修改这些信息
    document_info = {
        'org_name': '港信期货有限公司',
        'doc_number': '港信期货〔2025〕004号',
        'signer': '人事部',
        'title': '关于加强考勤管理的通知',
        'recipient': '各部门',
        'content': '''为进一步规范考勤管理，提高工作效率，现就有关事项通知如下：

一、严格执行考勤制度
全体员工要严格按照公司考勤制度执行，按时上下班，不得迟到早退。

二、完善请假手续
员工因事请假须提前申请，填写请假单，经部门负责人批准后方可离岗。

三、加强考勤监督
各部门要加强对员工考勤情况的监督检查，及时发现和纠正违规行为。

请各部门认真贯彻执行。''',
        'attachments': ['考勤管理制度', '请假申请表'],
        'date': '2025年1月16日'
    }
    
    # 生成文档
    generator.add_header(
        document_info['org_name'], 
        document_info['doc_number'], 
        document_info['signer']
    )
    generator.add_title(document_info['title'])
    generator.add_recipient(document_info['recipient'])
    generator.add_content(document_info['content'])
    generator.add_attachments(document_info['attachments'])
    generator.add_footer(document_info['org_name'], document_info['date'])
    
    # 保存文档
    filename = "考勤管理通知.docx"
    if generator.save_document(filename):
        print(f"✓ 文档已生成: {filename}")
    else:
        print("✗ 文档生成失败")


def main():
    """主函数 - 运行所有示例"""
    print("=== 公文生成工具使用示例 ===")
    print("本脚本演示如何使用公文生成工具创建不同类型的文档")
    print()
    
    try:
        # 运行示例
        create_simple_notice()
        print()
        
        create_meeting_notice()
        print()
        
        create_custom_document()
        print()
        
        print("=== 示例运行完成 ===")
        print("生成的文档文件:")
        print("- 元旦放假通知.docx")
        print("- 季度工作会议通知.docx")
        print("- 考勤管理通知.docx")
        print()
        print("您可以打开这些文档查看生成效果，")
        print("然后根据需要修改代码中的内容来生成自己的文档。")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        print("请确保已正确安装所有依赖包")


if __name__ == "__main__":
    main()
