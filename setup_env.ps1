# 公文自动生成工具环境安装脚本
# 适用于Windows PowerShell + conda环境

Write-Host "=== 公文自动生成工具环境配置 ===" -ForegroundColor Green

# 检查conda是否已安装
try {
    conda --version
    Write-Host "✓ Conda已安装" -ForegroundColor Green
} catch {
    Write-Host "✗ 未检测到Conda，请先安装Anaconda或Miniconda" -ForegroundColor Red
    Write-Host "下载地址: https://www.anaconda.com/products/distribution" -ForegroundColor Yellow
    exit 1
}

# 创建虚拟环境
Write-Host "正在创建Python虚拟环境..." -ForegroundColor Yellow
conda create -n document_generator python=3.9 -y

# 激活环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
conda activate document_generator

# 安装依赖包
Write-Host "安装依赖包..." -ForegroundColor Yellow
pip install -r requirements.txt

Write-Host "=== 环境配置完成 ===" -ForegroundColor Green
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "1. 激活环境: conda activate document_generator" -ForegroundColor White
Write-Host "2. 运行程序: python document_generator.py" -ForegroundColor White
Write-Host "3. 退出环境: conda deactivate" -ForegroundColor White
