#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公文生成工具测试脚本
用于验证文档生成功能是否正常工作
"""

import os
import sys
import tempfile
from document_generator import DocumentGenerator, load_config


def test_basic_document_generation():
    """测试基本文档生成功能"""
    print("测试1: 基本文档生成功能...")
    
    try:
        # 创建文档生成器
        generator = DocumentGenerator()
        
        # 添加各个部分
        generator.add_header("测试机关", "测试字号〔2025〕001号", "测试签发人")
        generator.add_title("测试文档标题")
        generator.add_recipient("测试接收机关")
        generator.add_content("这是测试正文内容。\n这是第二段测试内容。")
        generator.add_attachments(["测试附件1", "测试附件2"])
        generator.add_footer("测试机关", "2025年1月1日")
        
        # 保存到临时文件
        temp_file = "test_output.docx"
        success = generator.save_document(temp_file)
        
        if success and os.path.exists(temp_file):
            print("✓ 基本文档生成测试通过")
            # 清理测试文件
            os.remove(temp_file)
            return True
        else:
            print("✗ 基本文档生成测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 基本文档生成测试出错: {e}")
        return False


def test_config_file_loading():
    """测试配置文件加载功能"""
    print("测试2: 配置文件加载功能...")
    
    try:
        # 测试加载现有配置文件
        config = load_config("document_config.yaml")
        
        if config and isinstance(config, dict):
            required_fields = ['org_name', 'title', 'content']
            missing_fields = [field for field in required_fields if field not in config]
            
            if not missing_fields:
                print("✓ 配置文件加载测试通过")
                return True
            else:
                print(f"✗ 配置文件缺少必要字段: {missing_fields}")
                return False
        else:
            print("✗ 配置文件加载失败或格式错误")
            return False
            
    except Exception as e:
        print(f"✗ 配置文件加载测试出错: {e}")
        return False


def test_complete_workflow():
    """测试完整工作流程"""
    print("测试3: 完整工作流程...")
    
    try:
        # 加载配置
        config = load_config("document_config.yaml")
        if not config:
            print("✗ 无法加载配置文件")
            return False
        
        # 创建生成器并生成文档
        generator = DocumentGenerator()
        
        # 按照主程序的逻辑生成文档
        generator.add_header(
            config['org_name'], 
            config.get('doc_number'), 
            config.get('signer')
        )
        generator.add_title(config['title'])
        generator.add_recipient(config.get('recipient'))
        generator.add_content(config['content'])
        
        if config.get('attachments'):
            generator.add_attachments(config['attachments'])
        
        generator.add_footer(config['org_name'], config.get('date'))
        
        # 保存测试文档
        test_output = "workflow_test.docx"
        success = generator.save_document(test_output)
        
        if success and os.path.exists(test_output):
            print("✓ 完整工作流程测试通过")
            print(f"  生成的测试文档: {test_output}")
            return True
        else:
            print("✗ 完整工作流程测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 完整工作流程测试出错: {e}")
        return False


def test_error_handling():
    """测试错误处理功能"""
    print("测试4: 错误处理功能...")
    
    try:
        # 测试加载不存在的配置文件
        config = load_config("nonexistent_config.yaml")
        if config is None:
            print("✓ 不存在配置文件的错误处理正确")
        else:
            print("✗ 不存在配置文件的错误处理失败")
            return False
        
        # 测试保存到无效路径
        generator = DocumentGenerator()
        generator.add_title("测试标题")
        
        # 尝试保存到无效路径（包含非法字符）
        invalid_path = "/invalid/path/test.docx" if os.name != 'nt' else "C:\\invalid\\path\\test.docx"
        success = generator.save_document(invalid_path)
        
        if not success:
            print("✓ 无效路径的错误处理正确")
            return True
        else:
            print("✗ 无效路径的错误处理失败")
            return False
            
    except Exception as e:
        print(f"✓ 错误处理功能正常（捕获到预期异常: {type(e).__name__}）")
        return True


def run_all_tests():
    """运行所有测试"""
    print("=== 公文生成工具测试套件 ===")
    print()
    
    tests = [
        test_basic_document_generation,
        test_config_file_loading,
        test_complete_workflow,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
        print()
    
    print("=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    # 检查依赖
    try:
        from docx import Document
        import yaml
        import click
    except ImportError as e:
        print(f"缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        sys.exit(1)
    
    # 运行测试
    success = run_all_tests()
    sys.exit(0 if success else 1)
